import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, User, Plus } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import { IdentityModal } from './IdentityModal';
import type { User as UserType } from '../../types';

interface AuthModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAuthenticated: (user: UserType) => void;
}

export const AuthModal: React.FC<AuthModalProps> = ({
    isOpen,
    onClose,
    onAuthenticated
}) => {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [identities, setIdentities] = useState<any[]>([]);
    const { isLoading, loadIdentities, loginWithIdentity } = useIdentityManagement();

    useEffect(() => {
        if (isOpen) {
            const loadedIdentities = loadIdentities();
            setIdentities(loadedIdentities);
        }
    }, [isOpen, loadIdentities]);

    const handleIdentityCreated = (user: UserType) => {
        setShowCreateModal(false);
        const loadedIdentities = loadIdentities();
        setIdentities(loadedIdentities);
        onAuthenticated(user);
        onClose();
    };

    const handleIdentitySelect = async (identity: any) => {
        const password = prompt(`Enter password for ${identity.name}:`);
        if (!password) return;

        const result = await loginWithIdentity(identity, password);
        
        if (result.success && result.user) {
            onAuthenticated(result.user);
            onClose();
        } else {
            alert(result.error || 'Authentication failed. Please check your password.');
        }
    };

    if (!isOpen) return null;

    const modalContent = (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div className="flex items-center justify-between p-6 border-b">
                    <h2 className="text-xl font-semibold text-gray-900">Select Identity</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <X size={24} />
                    </button>
                </div>

                <div className="p-6">
                    {isLoading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-600">Loading...</p>
                        </div>
                    ) : identities.length > 0 ? (
                        <div className="space-y-3">
                            {identities.map((identity, index) => (
                                <button
                                    key={index}
                                    onClick={() => handleIdentitySelect(identity)}
                                    className="w-full flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                                >
                                    <User className="h-5 w-5 text-gray-400 mr-3" />
                                    <div>
                                        <div className="font-medium text-gray-900">{identity.name}</div>
                                        <div className="text-sm text-gray-500">
                                            {identity.publicKey.substring(0, 16)}...
                                        </div>
                                    </div>
                                </button>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                            <p className="text-gray-600 mb-4">No identities found</p>
                            <p className="text-sm text-gray-500">Create your first identity to get started</p>
                        </div>
                    )}

                    <div className="mt-6 pt-4 border-t">
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            <Plus className="h-4 w-4 mr-2" />
                            Create New Identity
                        </button>
                    </div>
                </div>
            </div>

            {showCreateModal && (
                <IdentityModal
                    isOpen={showCreateModal}
                    onClose={() => setShowCreateModal(false)}
                    onIdentityCreated={handleIdentityCreated}
                />
            )}
        </div>
    );

    return createPortal(modalContent, document.body);
};
