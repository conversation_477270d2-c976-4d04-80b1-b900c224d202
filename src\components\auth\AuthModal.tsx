import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, User, Plus } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import { IdentityModal } from './IdentityModal';
import type { User as UserType } from '../../types';

interface AuthModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAuthenticated: (user: UserType) => void;
}

export const AuthModal: React.FC<AuthModalProps> = ({
    isOpen,
    onClose,
    onAuthenticated
}) => {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [identities, setIdentities] = useState<any[]>([]);
    const { isLoading, loadIdentities, loginWithIdentity } = useIdentityManagement();

    useEffect(() => {
        if (isOpen) {
            const loadedIdentities = loadIdentities();
            setIdentities(loadedIdentities);
        }
    }, [isOpen, loadIdentities]);

    const handleIdentityCreated = (user: UserType) => {
        setShowCreateModal(false);
        // Reload identities after creating a new one
        const loadedIdentities = loadIdentities();
        setIdentities(loadedIdentities);
        onAuthenticated(user);
        onClose();
    };

    const handleIdentitySelect = async (identity: any) => {
        const password = prompt(`Enter password for ${identity.name}:`);
        if (!password) return;

        const result = await loginWithIdentity(identity, password);

        if (result.success && result.user) {
            onAuthenticated(result.user);
            onClose();
        } else {
            alert(result.error || 'Authentication failed. Please check your password.');
        }
    };

    if (!isOpen) return null;

    const modalContent = (
        <div 
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999] p-4"
            style={{ 
                position: 'fixed', 
                top: 0, 
                left: 0, 
                right: 0, 
                bottom: 0,
                display: showCreateModal ? 'none' : 'flex'
            }}
        >
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto border border-gray-700 shadow-2xl relative">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white">Welcome to Momentum</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white"
                        disabled={isLoading}
                    >
                        <X size={20} />
                    </button>
                </div>

                <p className="text-gray-300 mb-6 text-center">
                    Please authenticate to access your projects and tasks.
                </p>

                <div className="mb-6">
                    <h3 className="text-lg font-medium text-white mb-4 text-center">
                        Authentication Required
                    </h3>
                    <p className="text-gray-400 text-sm text-center mb-4">
                        Please sign in to continue
                    </p>
                </div>

                {identities.length === 0 ? (
                    <div className="text-center py-8">
                        <User size={48} className="mx-auto text-gray-500 mb-4" />
                        <h3 className="text-lg font-medium text-white mb-2">No Identities Found</h3>
                        <p className="text-gray-400 mb-6">
                            Create your first identity to get started with Momentum.
                        </p>
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-6 rounded-lg transition-colors flex items-center gap-2 mx-auto"
                            disabled={isLoading}
                        >
                            <Plus size={20} />
                            <span>Create New Identity</span>
                        </button>
                    </div>
                ) : (
                    <div>
                        <h3 className="text-lg font-medium text-white mb-4">Select Identity</h3>
                        <div className="space-y-3 mb-6">
                            {identities.map((identity) => (
                                <button
                                    key={identity.name}
                                    onClick={() => handleIdentitySelect(identity)}
                                    className="w-full bg-gray-700 hover:bg-gray-600 text-white p-4 rounded-lg transition-colors text-left flex items-center gap-3"
                                    disabled={isLoading}
                                >
                                    <User size={20} className="text-indigo-400" />
                                    <div>
                                        <div className="font-medium">{identity.name}</div>
                                        <div className="text-sm text-gray-400">
                                            {identity.publicKey.slice(0, 16)}...
                                        </div>
                                    </div>
                                </button>
                            ))}
                        </div>
                        
                        <div className="text-center">
                            <button
                                onClick={() => setShowCreateModal(true)}
                                className="text-indigo-400 hover:text-indigo-300 font-medium flex items-center gap-2 mx-auto"
                                disabled={isLoading}
                            >
                                <Plus size={16} />
                                <span>Create New Identity</span>
                            </button>
                        </div>
                    </div>
                )}

                <div className="text-center mt-6 pt-4 border-t border-gray-700 text-xs text-gray-400">
                    <p>🔒 Your identity is stored securely on your device</p>
                    <p>🌐 No data is sent to external servers</p>
                </div>
            </div>
        </div>
    );

    return (
        <>
            {createPortal(modalContent, document.body)}
            <IdentityModal
                isOpen={showCreateModal}
                onClose={() => setShowCreateModal(false)}
                onIdentityCreated={handleIdentityCreated}
            />
        </>
    );
};
