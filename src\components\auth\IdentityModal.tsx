import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, User, Lock } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import type { User as UserType } from '../../types';

interface IdentityModalProps {
    isOpen: boolean;
    onClose: () => void;
    onIdentityCreated: (user: UserType) => void;
}

export const IdentityModal: React.FC<IdentityModalProps> = ({
    isOpen,
    onClose,
    onIdentityCreated
}) => {
    const [step, setStep] = useState<'form' | 'entropy' | 'creating'>('form');
    const [name, setName] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [entropyCollected, setEntropyCollected] = useState(0);
    const [entropy] = useState(new Uint8Array(32));
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const { createIdentity } = useIdentityManagement();

    const requiredEntropy = 256;

    useEffect(() => {
        if (!isOpen || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const fixedWidth = 400;
        const fixedHeight = 128;
        canvas.width = fixedWidth;
        canvas.height = fixedHeight;

        ctx.fillStyle = '#1F2937';
        ctx.fillRect(0, 0, fixedWidth, fixedHeight);

        const handleMouseMove = (e: MouseEvent) => {
            if (entropyCollected >= requiredEntropy) return;

            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (fixedWidth / rect.width);
            const y = (e.clientY - rect.top) * (fixedHeight / rect.height);
            const time = Date.now();

            const entropyByte = (x ^ y ^ time) & 0xFF;
            const index = entropyCollected % 32;
            entropy[index] ^= entropyByte;

            setEntropyCollected(prev => Math.min(prev + 1, requiredEntropy));

            ctx.fillStyle = '#4F46E5';
            ctx.fillRect(x - 2, y - 2, 4, 4);
        };

        canvas.addEventListener('mousemove', handleMouseMove);
        return () => canvas.removeEventListener('mousemove', handleMouseMove);
    }, [isOpen, entropyCollected, entropy, requiredEntropy]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (password !== confirmPassword) {
            alert('Passwords do not match');
            return;
        }
        if (password.length < 8) {
            alert('Password must be at least 8 characters long');
            return;
        }
        setStep('entropy');
    };

    const handleCreateIdentity = async () => {
        if (entropyCollected < requiredEntropy) {
            alert('Please collect more entropy by moving your mouse');
            return;
        }

        setStep('creating');
        const result = await createIdentity(name, password, entropy);

        if (result.success && result.identity) {
            const user: UserType = {
                name: result.identity.name,
                publicKey: result.identity.publicKey,
                uid: result.identity.publicKey
            };
            onIdentityCreated(user);
        } else {
            alert(result.error || 'Failed to create identity');
            setStep('entropy');
        }
    };

    if (!isOpen) return null;

    const modalContent = (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10001]">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div className="flex items-center justify-between p-6 border-b">
                    <h2 className="text-xl font-semibold text-gray-900">Create New Identity</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <X size={24} />
                    </button>
                </div>

                <div className="p-6">
                    {step === 'form' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                                    Identity Name
                                </label>
                                <div className="relative">
                                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="text"
                                        id="name"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Enter your name"
                                        required
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                    Password
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="password"
                                        id="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Enter password"
                                        required
                                        minLength={8}
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                    Confirm Password
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="password"
                                        id="confirmPassword"
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Confirm password"
                                        required
                                        minLength={8}
                                    />
                                </div>
                            </div>

                            <button
                                type="submit"
                                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Continue
                            </button>
                        </form>
                    )}

                    {step === 'entropy' && (
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">Entropy Collection</h3>
                                <p className="text-sm text-gray-600 mb-4">
                                    Move your mouse around the area below to generate randomness for your identity.
                                </p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                    <span>Progress:</span>
                                    <span>{Math.round((entropyCollected / requiredEntropy) * 100)}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${(entropyCollected / requiredEntropy) * 100}%` }}
                                    ></div>
                                </div>
                            </div>

                            <canvas
                                ref={canvasRef}
                                className="w-full h-32 bg-gray-800 rounded border-2 border-dashed border-gray-600 cursor-crosshair"
                                style={{ 
                                    touchAction: 'none',
                                    width: '100%',
                                    height: '128px',
                                    maxWidth: '400px',
                                    display: 'block'
                                }}
                            />

                            <div className="flex space-x-3">
                                <button
                                    onClick={() => setStep('form')}
                                    className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
                                >
                                    Back
                                </button>
                                <button
                                    onClick={handleCreateIdentity}
                                    disabled={entropyCollected < requiredEntropy}
                                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                                >
                                    Create Identity
                                </button>
                            </div>
                        </div>
                    )}

                    {step === 'creating' && (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600 mb-2">Creating your identity...</p>
                            <p className="text-sm text-gray-500">This may take a moment</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );

    return createPortal(modalContent, document.body);
};
