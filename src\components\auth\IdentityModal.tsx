import React, { useState, useRef, useEffect } from 'react';
import { X } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import type { User } from '../../types';

interface IdentityModalProps {
    isOpen: boolean;
    onClose: () => void;
    onIdentityCreated: (user: User) => void;
}

export const IdentityModal: React.FC<IdentityModalProps> = ({
    isOpen,
    onClose,
    onIdentityCreated
}) => {
    const [name, setName] = useState('');
    const [password, setPassword] = useState('');
    const [entropyCollected, setEntropyCollected] = useState(0);
    const [entropy] = useState(() => new Uint8Array(32));
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const { isLoading, createIdentity } = useIdentityManagement();

    const requiredEntropy = 256;
    const entropyProgress = Math.min((entropyCollected / requiredEntropy) * 100, 100);

    useEffect(() => {
        if (isOpen) {
            // Reset form when modal opens
            setName('');
            setPassword('');
            setEntropyCollected(0);
            entropy.fill(0);
        }
    }, [isOpen, entropy]);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas || !isOpen) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas size
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        // Clear canvas
        ctx.fillStyle = '#1f2937';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const handleMouseMove = (e: MouseEvent) => {
            if (entropyCollected >= requiredEntropy) return;

            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const time = Date.now();

            // Collect entropy from mouse movement
            const entropyByte = (x ^ y ^ time) & 0xFF;
            const index = entropyCollected % 32;
            entropy[index] ^= entropyByte;

            setEntropyCollected(prev => Math.min(prev + 1, requiredEntropy));

            // Draw on canvas
            ctx.fillStyle = `hsl(${(entropyCollected * 137.5) % 360}, 70%, 60%)`;
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        };

        canvas.addEventListener('mousemove', handleMouseMove);

        return () => {
            canvas.removeEventListener('mousemove', handleMouseMove);
        };
    }, [isOpen, entropyCollected, entropy, requiredEntropy]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (entropyCollected < requiredEntropy) {
            alert('Please collect more entropy by moving your mouse over the canvas.');
            return;
        }

        const result = await createIdentity(name, password, entropy);
        
        if (result.success && result.identity) {
            alert(`Identity for "${name}" created! Please log in with your new identity.`);
            
            // Create user object for immediate login
            const user: User = {
                name: result.identity.name,
                publicKey: result.identity.publicKey,
                uid: result.identity.publicKey
            };
            
            onIdentityCreated(user);
            onClose();
        } else {
            alert(result.error || 'Failed to create identity. Please try again.');
        }
    };

    const handleClose = () => {
        setName('');
        setPassword('');
        setEntropyCollected(0);
        entropy.fill(0);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white">Create New Identity</h2>
                    <button
                        onClick={handleClose}
                        className="text-gray-400 hover:text-white"
                        disabled={isLoading}
                    >
                        <X size={20} />
                    </button>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="create-name" className="block text-gray-300 text-sm font-bold mb-2">
                            Name
                        </label>
                        <input
                            type="text"
                            id="create-name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            required
                            disabled={isLoading}
                        />
                    </div>

                    <div className="mb-6">
                        <label htmlFor="create-password" className="block text-gray-300 text-sm font-bold mb-2">
                            Password
                        </label>
                        <input
                            type="password"
                            id="create-password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            required
                            disabled={isLoading}
                        />
                    </div>

                    <div className="mb-6">
                        <label className="block text-gray-300 text-sm font-bold mb-2">
                            Entropy Collection ({entropyProgress.toFixed(1)}%)
                        </label>
                        <div className="bg-gray-700 rounded-lg p-4">
                            <div className="w-full bg-gray-600 rounded-full h-2 mb-3">
                                <div 
                                    className="bg-indigo-600 h-2 rounded-full transition-all duration-300" 
                                    style={{ width: `${entropyProgress}%` }}
                                ></div>
                            </div>
                            <canvas
                                ref={canvasRef}
                                className="w-full h-32 bg-gray-800 rounded border-2 border-dashed border-gray-600 cursor-crosshair"
                                style={{ touchAction: 'none' }}
                            />
                            <p className="text-xs text-gray-400 mt-2">
                                Move your mouse over the canvas to collect entropy for secure key generation
                            </p>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <button
                            type="button"
                            onClick={handleClose}
                            className="text-gray-400 hover:text-white"
                            disabled={isLoading}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors disabled:bg-gray-600"
                            disabled={isLoading || entropyProgress < 100}
                        >
                            {isLoading ? 'Generating...' : 'Generate & Save Identity'}
                        </button>
                    </div>
                </form>

                <div className="text-center mt-6 text-sm text-gray-400">
                    <p>🔒 Your identity is stored securely on your device</p>
                    <p>🌐 No data is sent to external servers</p>
                </div>
            </div>
        </div>
    );
};
