import { useState, useMemo } from 'react';

// Import types
import type { Task } from './types';

// Import hooks
import { useAuth, useData } from './hooks';

// Import components
import { AuthModal } from './components/auth';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Header } from './components/layout';
import {
    DashboardView,
    DayPlannerView,
    NoProjectsView,
    BoardView,
    ListView,
    CalendarView,
    GanttView
} from './components/views';
import { LoadingSpinner, ProjectModal, TaskModal } from './components/ui';

// --- Main App Component ---
export default function App() {
    // Clear any existing session for debugging
    if (process.env.NODE_ENV === 'development') {
        sessionStorage.removeItem('momentum_session');
    }

    // Authentication
    const { user, setUser, isAuthReady, handleLogout } = useAuth();

    // Data management
    const {
        projects,
        activeProject,
        setActiveProject,
        tasks,
        allTasks,
        isLoading,
        statuses,
        allMilestones,
        tasksByStatus,
        tasksWithDates,
        handleSaveProject,
        handleSaveTask,
        handleAddComment,
        handleDeleteTask,
        clearUserData
    } = useData(user, isAuthReady);

    // UI state
    const [showTaskModal, setShowTaskModal] = useState(false);
    const [showProjectModal, setShowProjectModal] = useState(false);
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [currentView, setCurrentView] = useState('Dashboard');
    const [isDrawerOpen, setIsDrawerOpen] = useState(true);
    const [plannerDate, setPlannerDate] = useState(new Date());

    // Memoized data for daily tasks
    const dailyTasks = useMemo(() => {
        if (currentView !== 'Day' || projects.length === 0) return [];
        const startOfDay = new Date(plannerDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(plannerDate);
        endOfDay.setHours(23, 59, 59, 999);
        return allTasks
            .filter(t => t.dueDate && new Date(t.dueDate) >= startOfDay && new Date(t.dueDate) <= endOfDay)
            .map(t => ({...t, projectName: projects.find(p => p.id === t.projectId)?.name }))
            .sort((a,b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime());
    }, [currentView, plannerDate, allTasks, projects]);

    // Local handlers
    const handleNewTaskForDate = (date: Date) => {
        if (!activeProject) {
            alert("Please select a project first.");
            return;
        }
        const isoDate = date.toISOString().split('T')[0];
        setSelectedTask({ startDate: isoDate, dueDate: isoDate } as Task);
        setShowTaskModal(true);
    };

    const handleTaskSave = (taskData: Partial<Task>) => {
        handleSaveTask(taskData);
        setShowTaskModal(false);
        setSelectedTask(null);
    };

    const handleProjectSave = (projectName: string) => {
        handleSaveProject(projectName);
        setCurrentView('Board');
        setShowProjectModal(false);
    };

    const handleUserLogout = () => {
        if (handleLogout()) {
            clearUserData();
            setCurrentView('Dashboard');
        }
    };

    // Render Logic
    if (!isAuthReady) return <LoadingSpinner fullScreen={true} />;

    const renderView = () => {
        if (!activeProject && projects.length > 0 && !isLoading) {
            setActiveProject(projects[0]);
            return <LoadingSpinner />;
        }

        switch(currentView) {
            case 'Dashboard':
                return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
            case 'Day':
                return <DayPlannerView
                    tasks={dailyTasks}
                    date={plannerDate}
                    setDate={setPlannerDate}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                />;
            case 'Board':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <BoardView
                    tasks={tasks}
                    setTasks={() => {}} // This will be handled by the hook
                    statuses={statuses}
                    tasksByStatus={tasksByStatus}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onDeleteTask={handleDeleteTask}
                    onAddTask={() => handleNewTaskForDate(new Date())}
                    activeProject={activeProject}
                    user={user!}
                />;
            case 'List':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <ListView
                    statuses={statuses}
                    tasksByStatus={tasksByStatus}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onDeleteTask={handleDeleteTask}
                />;
            case 'Calendar':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <CalendarView
                    tasks={tasks}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onNewTaskForDate={handleNewTaskForDate}
                />;
            case 'Gantt':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <GanttView
                    tasks={tasksWithDates}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                />;
            default:
                return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
        }
    };

    return (
        <>
            <div className="flex h-screen bg-gray-900 text-gray-100 font-sans">
                {user && (
                    <AppDrawer
                        isOpen={isDrawerOpen}
                        projects={projects}
                        activeProject={activeProject}
                        setActiveProject={setActiveProject}
                        onNewProject={() => setShowProjectModal(true)}
                        currentView={currentView}
                        setCurrentView={setCurrentView}
                        onLogout={handleUserLogout}
                        user={user}
                    />
                )}
                <div className="flex-1 flex flex-col overflow-hidden">
                    {user ? (
                        <Header
                            onAddTask={() => handleNewTaskForDate(new Date())}
                            isDrawerOpen={isDrawerOpen}
                            toggleDrawer={() => setIsDrawerOpen(!isDrawerOpen)}
                            activeProject={activeProject}
                            currentView={currentView}
                            user={user}
                            onLogout={handleUserLogout}
                        />
                    ) : (
                        <header className="flex items-center justify-center p-4 border-b border-gray-700/80 bg-gray-900/80 backdrop-blur-sm z-10 flex-shrink-0">
                            <h1 className="text-2xl font-bold text-white">Momentum</h1>
                        </header>
                    )}
                    <main className="flex-1 overflow-y-auto bg-gray-900">
                        <div className="p-4 md:p-6 lg:p-8">
                            {user ? renderView() : (
                                <div className="text-center text-gray-400 py-20">
                                    <h2 className="text-2xl font-bold mb-4">Welcome to Momentum</h2>
                                    <p>Please authenticate to access your projects and tasks.</p>
                                </div>
                            )}
                        </div>
                    </main>
                </div>
            </div>

            {/* Authentication Modal - Rendered outside main container */}
            <AuthModal
                isOpen={true} // Force show for debugging
                onClose={() => {}} // No-op since modal should always be open when no user
                onAuthenticated={setUser}
            />

            {/* Debug: Force show modal for testing */}
            {process.env.NODE_ENV === 'development' && (
                <div className="fixed top-4 right-4 z-[10001] bg-yellow-600 text-black p-2 rounded text-xs">
                    Debug: user={user ? `logged in as ${user.name}` : 'null'}, authReady={isAuthReady ? 'true' : 'false'}, modal={(!user && isAuthReady) ? 'should show' : 'hidden'}
                </div>
            )}

            {/* Force show AuthModal for debugging */}
            {process.env.NODE_ENV === 'development' && !user && (
                <div className="fixed top-16 right-4 z-[10001] bg-red-600 text-white p-2 rounded text-xs">
                    AuthModal should be visible now!
                </div>
            )}
            {showTaskModal && (
                <TaskModal
                    task={selectedTask}
                    project={activeProject || projects.find(p => p.id === selectedTask?.projectId)}
                    onClose={() => {setShowTaskModal(false); setSelectedTask(null);}}
                    onSave={handleTaskSave}
                    onDelete={handleDeleteTask}
                    onAddComment={handleAddComment}
                    statuses={statuses}
                />
            )}
            {showProjectModal && (
                <ProjectModal
                    onClose={() => setShowProjectModal(false)}
                    onSave={handleProjectSave}
                />
            )}
        </>
    );
}









