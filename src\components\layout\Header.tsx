import React, { useState, useRef, useEffect } from 'react';
import { Plus, ChevronsLeft, ChevronsRight, User, ChevronDown, LogOut, UserPlus } from 'lucide-react';
import { IdentityMenu, IdentityModal } from '../auth';
import type { HeaderProps } from '../../types';

export const Header: React.FC<HeaderProps> = ({ onAddTask, isDrawerOpen, toggleDrawer, activeProject, currentView, user, onLogout }) => {
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const [isIdentityMenuOpen, setIsIdentityMenuOpen] = useState(false);
    const [isIdentityModalOpen, setIsIdentityModalOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleIdentitySwitch = (newUser: any) => {
        // This will trigger a re-render with the new user
        window.location.reload(); // Simple approach to refresh the app with new identity
    };

    const handleIdentityCreated = (newUser: any) => {
        // This will trigger a re-render with the new user
        window.location.reload(); // Simple approach to refresh the app with new identity
    };
    const viewTitles: Record<string, string> = {
        'Dashboard': 'Dashboard',
        'Day': 'Day Planner',
        'Board': activeProject?.name || '',
        'List': activeProject?.name || '',
        'Calendar': activeProject?.name || '',
        'Gantt': activeProject?.name || ''
    };
    
    return (
        <header className="flex items-center justify-between p-4 border-b border-gray-700/80 bg-gray-900/80 backdrop-blur-sm z-10 flex-shrink-0">
            <div className="flex items-center space-x-4">
                <button onClick={toggleDrawer} className="p-2 rounded-md hover:bg-gray-700">
                    {isDrawerOpen ? <ChevronsLeft size={20}/> : <ChevronsRight size={20} />}
                </button>
                <h1 className="text-2xl font-bold text-white">{viewTitles[currentView] || 'Momentum'}</h1>
            </div>
            <div className="flex items-center space-x-4">
                {activeProject &&
                    <button
                        onClick={onAddTask}
                        className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-75"
                    >
                        <Plus size={18} />
                        <span className="hidden md:inline">New Task</span>
                    </button>
                }

                {/* User Menu */}
                <div className="relative" ref={menuRef}>
                    <button
                        onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-75"
                    >
                        <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                            <User size={16} className="text-white" />
                        </div>
                        <span className="hidden md:inline text-sm font-medium">{user.name}</span>
                        <ChevronDown size={16} className={`transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {/* Dropdown Menu */}
                    {isUserMenuOpen && (
                        <div className="absolute right-0 mt-2 w-64 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-50">
                            <div className="p-3 border-b border-gray-700">
                                <p className="font-semibold text-white">{user.name}</p>
                                <p className="text-xs text-gray-400 font-mono">{user.publicKey.substring(0, 24)}...</p>
                            </div>
                            <div className="py-2">
                                <button
                                    onClick={() => {
                                        setIsUserMenuOpen(false);
                                        setIsIdentityMenuOpen(true);
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center space-x-2"
                                >
                                    <User size={16} />
                                    <span>Switch Identity</span>
                                </button>
                                <button
                                    onClick={() => {
                                        setIsUserMenuOpen(false);
                                        setIsIdentityModalOpen(true);
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center space-x-2"
                                >
                                    <UserPlus size={16} />
                                    <span>Create New Identity</span>
                                </button>
                                <hr className="my-2 border-gray-700" />
                                <button
                                    onClick={() => {
                                        setIsUserMenuOpen(false);
                                        onLogout();
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 flex items-center space-x-2"
                                >
                                    <LogOut size={16} />
                                    <span>Logout</span>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Identity Management Modals */}
            <IdentityMenu
                isOpen={isIdentityMenuOpen}
                onClose={() => setIsIdentityMenuOpen(false)}
                onIdentitySwitch={handleIdentitySwitch}
                onCreateIdentity={() => {
                    setIsIdentityMenuOpen(false);
                    setIsIdentityModalOpen(true);
                }}
                currentUser={user}
            />

            <IdentityModal
                isOpen={isIdentityModalOpen}
                onClose={() => setIsIdentityModalOpen(false)}
                onIdentityCreated={handleIdentityCreated}
            />
        </header>
    );
};
